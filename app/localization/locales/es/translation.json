{"navigation": {"dashboard": "Panel Principal", "people": "Personas", "activity": "Actividad", "gallery": "Galería", "moments": "Momentos", "settings": "Configuración"}, "actions": {"save": "Guardar", "cancel": "<PERSON><PERSON><PERSON>", "delete": "Eliminar", "edit": "<PERSON><PERSON>", "add": "Agregar", "back": "Atrás", "next": "Siguient<PERSON>", "done": "<PERSON><PERSON>", "close": "<PERSON><PERSON><PERSON>", "confirm": "Confirmar", "yes": "Sí", "no": "No"}, "authentication": {"signIn": "<PERSON><PERSON><PERSON>", "signOut": "<PERSON><PERSON><PERSON>", "username": "Usuario", "password": "Contraseña", "pinCode": "Código PIN", "supplementalPinCode": "Código PIN Suplementario", "forgotPassword": "<PERSON><PERSON><PERSON><PERSON> Contraseña", "resetPassword": "Restable<PERSON>", "newPassword": "Nueva Contraseña", "confirmPassword": "Con<PERSON><PERSON><PERSON>", "enterCode": "Ingresar Código", "emailAddress": "Dirección de Correo", "baseUrl": "URL Base", "passwordHelp": "Ayuda con la contraseña", "verifyPin": "Verificar PIN", "ssoLogin": "Inicio de Sesión SSO", "saveAndConnect": "Guardar y Conectar", "getConnectionStatus": "Obtener Estado de Conexión", "useUsernamePassword": "Usar Usuario y Contraseña", "usePinCode": "¿Ya iniciaste sesión? Usa tu código PIN", "enterUsernameFirst": "Por favor, ingresa tu usuario primero.", "forgotPasswordError": "Error al restablecer contraseña", "checkEmailVerification": "Por favor, revisa tu correo para el código de verificación", "mustEnterUsername": "Debes ingresar un usuario", "invalidCredentials": "Usuario o contraseña incorrectos", "networkError": "Error de red. Por favor intenta de nuevo.", "loginError": "Error al iniciar sesión. Por favor intenta de nuevo."}, "people": {"checkIn": "Registrar Entrada", "checkOut": "Registrar <PERSON><PERSON>", "move": "Mover", "moveToGroup": "Mover a...", "selectGroup": "Elegir grupo", "absentToday": "<PERSON><PERSON><PERSON>", "scheduledToday": "Programado Hoy", "notScheduledToday": "No Programado Hoy", "completedBy": "Completado Por", "chooseAction": "Elegir Acción", "sleepCheck": "Control de Sueño", "nameToFace": "Identificación", "search": "Buscar", "allGroups": "Todos los Grupos", "people": "Personas", "select": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "done": "<PERSON><PERSON>", "profile": "Perfil", "activity": "Actividad", "outlook": "Info <PERSON>", "notifications": "Notificaciones", "relationships": "Relaciones", "basicInfo": "Información Básica", "allergies": "Alergias", "importantNotes": "Notas Importantes", "specialNeeds": "Necesidades Especiales", "checkInOutlook": "Info de Entrada", "generatePortfolio": "Generar portafolio", "viewPortfolio": "Ver Portafolio", "dateRange": "<PERSON><PERSON>", "ageGroup": "Grupo de Edad", "chooseAgeGroup": "Elegir grupo de edad", "startDate": "Fecha de Inicio", "endDate": "<PERSON><PERSON>", "generate": "Generar", "noActiveSchedule": "Sin horario activo", "filters": "<PERSON><PERSON><PERSON>", "view": "VISTA", "list": "Lista", "grid": "Cuadrícula", "group": "GRUPO", "type": "TIPO", "families": "Familias", "staffAndAdmins": "Personal y Administradores", "sort": "ORDENAR", "checkedInFirst": "Registrados Primero", "alphabetical": "Alfabético (Apellido primero)", "myGroup": "Mi grupo", "otherGroup": "Otro grupo", "bathroom": "Baños", "split": "División", "extracurricular": "Extraescolar", "other": "<PERSON><PERSON>", "writeReason": "Escribe la razón", "chooseOneOption": "Es necesario elegir una opción.", "dontSave": "No guardar", "chooseValue": "Elige un valor", "rosterChanged": "Esta lista ha cambiado - realiza una identificación", "noMatchingResults": "No hay resultados que coincidan con tu búsqueda", "noResultsWithFilters": "No hay resultados que coincidan con tu texto de búsqueda y filtros", "searchAll": "Buscar Todo", "tagInMoment": "Etiquetar en Momento", "errorChecking": "Error: {{message}}", "successCheckedIn": "Éxito: {{count}} registrados", "successCheckedOut": "Éxito: {{count}} retirados", "mustSelectPerson": "Debes seleccionar al menos una persona para el control de sueño.", "successfulSleepChecks": "Controles de sueño exitosos: {{count}}", "sleepNotFound": "<PERSON><PERSON> no encontrado: {{count}}", "nameToFaceIssues": "Problemas de Identificación", "couldNotRetrieveData": "No se pudieron obtener los datos de identificación", "avatarOptions": "Opciones de avatar", "newFromCamera": "Nuevo desde cámara...", "newFromLibrary": "Nuevo desde galería...", "confirm": "Confirmar", "unspecifiedRelationship": "Relación No Especificada", "authorizedPickup": "recogida autorizada", "emergencyContact": "Contacto de Emergencia", "groups": "Grupos", "enterUpdatedValue": "Ingresa el valor actualizado para el campo"}, "forms": {"title": "<PERSON><PERSON><PERSON><PERSON>", "notes": "Notas", "reason": "Razón", "amount": "Cantidad", "type": "Tipo", "date": "<PERSON><PERSON>", "time": "<PERSON><PERSON>", "description": "Descripción"}, "status": {"checkedIn": "Registrado", "checkedOut": "<PERSON><PERSON><PERSON>", "absent": "Ausente", "present": "Presente", "scheduled": "Programado", "notScheduled": "No Programado"}, "common": {"loading": "Cargando...", "error": "Error", "success": "Éxito", "warning": "Advertencia", "info": "Información", "retry": "Reintentar", "refresh": "Actualizar", "save": "Guardar", "cancel": "<PERSON><PERSON><PERSON>"}, "settings": {"selectLanguage": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "language": "Idioma", "changeLanguage": "Cambiar I<PERSON>", "english": "Inglés", "spanish": "Español"}, "dashboard": {"headerTitle": "Panel Principal", "upNext": "A Continuación", "arrivingSoon": "{{count}} <PERSON><PERSON><PERSON><PERSON>", "leavingSoon": "{{count}} <PERSON><PERSON><PERSON>", "here": "{{count}} Aquí", "outlook": "Info <PERSON>", "announcements": "<PERSON><PERSON><PERSON><PERSON>", "greeting": "¡Hola {{firstName}}!", "checkedIntoGroup": "Estás registrado en {{groupName}}", "checkedIn": "Estás registrado", "notCheckedIn": "No estás registrado", "staffImpact": {"title": "<PERSON>", "defaultMessage": "¡Sé genial hoy! Revisa aquí para ver tu progreso."}, "lightbridgeResources": {"title": "Recursos Importantes", "description": "Aquí están los enlaces a recursos importantes:", "tortal": "<PERSON><PERSON>", "tortalDescription": "Entrenamiento Lightbridge", "intranet": "Intranet", "intranetDescription": "Noticias y Documentación Lightbridge"}, "lightbridgePeek": {"title": "<PERSON><PERSON> Semana", "header": "<PERSON><PERSON> Semana", "noDataMessage": "No hay vistazos disponibles para hoy. ¡Mantente atento!"}, "survey": {"title": "¿Cómo lo estamos haciendo?", "question": "¡Hicimos algunos cambios en la página principal! ¿Estás disfrutando esos cambios?", "happyFollowUp": "¡Genial! ¿Qué cambios te gustan:", "sadFollowUp": "¿Qué necesita mejora?:", "upNextSection": "Sección A Continuación", "outlookSection": "Sección Info Médica", "expressDriveUpSection": "Recogida Rápida", "announcementsSection": "<PERSON><PERSON><PERSON><PERSON>", "anythingElse": "¿Algo Más?", "submitFeedback": "Enviar Comentarios"}, "sleep": {"title": "<PERSON><PERSON><PERSON>"}, "timeCard": {"title": "Aprobación de Tarjeta de Tiempo", "reviewTitle": "Revisar Tarjetas de Tiempo", "period": "<PERSON><PERSON><PERSON>", "timeReported": "Tiempo Reportado", "changePeriod": "<PERSON><PERSON><PERSON>", "approveSelected": "<PERSON><PERSON><PERSON>", "pendingApproval": "Tienes tiempo para aprobar", "review": "Rev<PERSON><PERSON>"}, "dataValidation": {"title": "¿Está tu información actualizada?", "verifyButton": "Verificar Información"}, "tray": {"chatSupport": "Chatear con la escuela", "whatsNew": "Novedades", "learningPath": "Ruta de Aprendizaje"}, "campaign": {"resourcesTitle": "Recursos e Información", "surveyTitle": "¿Cómo lo estamos haciendo?", "viewButton": "<PERSON>er", "submitButton": "Enviar Comentarios", "cancelButton": "<PERSON><PERSON><PERSON>", "ratingResponse": "Nos diste un '{{rating}}'...", "thankYouMessage": "<PERSON>as gracias por tus comentarios.", "followUpQuestions": {"improve": "¿Cómo podemos hacerlo bien la próxima vez?", "delight": "¿Cómo podemos deleitarte la próxima vez?", "delighted": "¿Cómo te deleitamos?"}, "alerts": {"errorTitle": "Error", "errorMessage": "Experimentamos un error al guardar tu respuesta, por favor inténtalo de nuevo", "successTitle": "¡<PERSON><PERSON><PERSON>!", "successMessage": "Tus comentarios se enviaron exitosamente"}}}, "momentTypes": {"comment": "Comentario", "food": "<PERSON><PERSON><PERSON>", "potty": "<PERSON>ño", "sleep": "<PERSON><PERSON>", "activity": "Actividad", "medical": "Médico", "learning": "Aprendizaje", "mood": "<PERSON><PERSON><PERSON>", "checkIn": "Entrada", "checkOut": "Salida", "move": "Traslado", "incident": "<PERSON><PERSON>", "alert": "<PERSON><PERSON><PERSON>", "supplies": "Suministros", "illness": "Enfermedad", "ouch": "<PERSON><PERSON>", "bkHealthAlert": "<PERSON><PERSON><PERSON>"}, "momentEntry": {"moment": "Momento", "attribution": "Atribuido", "tag": "Etiquetar", "comment": "Comentario", "date": "<PERSON><PERSON>", "time": "<PERSON><PERSON>", "dateTime": "<PERSON><PERSON> y <PERSON>", "addMedia": "Agregar Multimedia", "overrideTitle": "Nueva Entrada", "timeCardWarning": "Inicia sesión en la aplicación web para editar tarjetas de tiempo."}, "momentFields": {"labels": {"type": "Tipo", "amount": "Cantidad", "engagement": "Participación", "mood": "Estado de Ánimo", "continence": "Continencia", "training": "Entrenamiento", "appliedOintment": "Ungüento Aplicado", "startTime": "Hora de Inicio", "endTime": "<PERSON><PERSON>", "didNotSleep": "No Durmió", "satQuietly": "<PERSON> <PERSON><PERSON>ó <PERSON>", "symptomsObserved": "Síntomas Observados", "supplyType": "Tipo de Suministro", "curriculum": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "selectMilestone": "Seleccionar un hito", "ageRange": "<PERSON><PERSON>", "activity": "Actividad", "observations": "Observaciones", "natureOfIncident": "Naturaleza del Incidente", "actionTaken": "Acción Tomada", "incidentLocation": "Ubicación del Incidente", "includeOnlyCheckedIn": "Incluir solo si está registrado ahora", "tagEveryone": "Etiquetar a todos (administradores, personal, familias)", "sendViaEmail": "Enviar por Correo Electrónico", "sendViaSMS": "Enviar por SMS (texto)", "sendViaPush": "Enviar por Push (aplicación)", "description": "Descripción", "careProvided": "Cuidado Proporcionado", "contactedParent": "Contactó a los Padres", "contactedDoctor": "<PERSON><PERSON>", "nurseNotified": "Enfermera Notificada", "professionalMedicationNecessary": "¿Medicación Profesional Necesaria?", "medicationType": "Tipo de Medicación", "medicationName": "Nombre del Medicamento", "doctorName": "Nombre del Doctor", "medication": "Medicamento", "administeredBy": "Administrado Por", "notesForStaff": "Notas para el Personal", "attendingToday": "<PERSON><PERSON><PERSON><PERSON>", "estimatedArrivalTime": "Hora Estimada de Llegada", "reasonNotAttending": "Razón por no asistir", "breastmilkOffered": "Leche Materna Ofrecida", "breastmilkConsumed": "Leche Materna Consumida", "formulaOffered": "<PERSON><PERSON><PERSON><PERSON>a", "formulaConsumed": "<PERSON><PERSON><PERSON><PERSON>", "milkOffered": "Leche Ofrecida", "milkConsumed": "Leche Consumida", "foodType": "<PERSON><PERSON><PERSON> de Comida", "amountEaten": "Cantidad Consumida", "time": "<PERSON><PERSON>"}, "values": {"pottyTypes": {"wet": "<PERSON><PERSON><PERSON>", "bm": "DF", "wetBm": "Mojado+DF", "dry": "Seco"}, "continenceTypes": {"continent": "<PERSON><PERSON><PERSON>", "incontinent": "Incontinente", "refused": "Se Negó"}, "pottyTraining": {"tried": "Intentó", "successful": "Exitoso", "accident": "Accidente"}, "foodTypes": {"breakfast": "<PERSON><PERSON><PERSON>", "amSnack": "Merienda AM", "lunch": "<PERSON><PERSON><PERSON><PERSON>", "pmSnack": "Me<PERSON><PERSON> PM", "lateSnack": "Merienda Tardía", "dinner": "<PERSON><PERSON>", "bottle": "Biberón", "babyFood": "<PERSON><PERSON><PERSON>", "cereal": "Cereal", "tube": "Tu<PERSON>"}, "foodAmounts": {"all": "Todo", "most": "La Mayoría", "some": "Algo", "none": "<PERSON><PERSON>", "notOffered": "No Ofrecido"}, "moodLevels": {"happy": "<PERSON><PERSON><PERSON>", "soSo": "Regular", "sad": "Triste"}, "activityEngagement": {"active": "Activo", "passive": "Pasivo", "refused": "Se Negó", "outOfCenter": "Fuera del Centro"}, "attendingOptions": {"yes": "Sí", "noSick": "No - enfermo", "noVacation": "No - vacaciones", "noOther": "No - otro"}, "arrivalTimes": {"tenMinutes": "~10 minutos", "twentyMinutes": "~20 minutos", "thirtyMinutes": "~30 minutos", "thirtyPlusMinutes": "30+ minutos", "removeArrivalTime": "Eliminar hora de llegada"}, "contactMethods": {"phone": "Teléfono", "email": "Correo Electrónico", "inPerson": "En Persona", "didNotNotify": "No Notificó"}, "babyFoodTypes": {"fruit": "<PERSON><PERSON>", "vegetable": "Verdura", "cereal": "Cereal", "meat": "<PERSON><PERSON>"}, "bottleTypes": {"formula": "<PERSON><PERSON><PERSON><PERSON>", "breastMilk": "Leche Materna", "water": "Agua", "juice": "Jugo"}}}}